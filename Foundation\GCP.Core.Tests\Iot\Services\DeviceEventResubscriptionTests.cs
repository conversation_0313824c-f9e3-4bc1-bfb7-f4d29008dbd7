using GCP.Common;
using GCP.DataAccess;
using GCP.Iot.Models;
using GCP.Iot.Services;
using Microsoft.Extensions.DependencyInjection;
using Xunit;
using Serilog;
using EasyCaching.Core;
using GCP.Eventbus.Infrastructure;
using LinqToDB;

namespace GCP.Core.Tests.Iot.Services
{
    /// <summary>
    /// 设备事件重新订阅测试
    /// </summary>
    public class DeviceEventResubscriptionTests : IDisposable
    {
        private readonly ServiceProvider _serviceProvider;
        private readonly IotEventIntegrationService _iotEventIntegration;
        private readonly EquipmentCommunicationManager _communicationManager;
        private readonly IMessageBusManager _messageBusManager;
        private readonly string _testEquipmentId = "test-equipment-001";
        private readonly string _testEventId = "test-event-001";

        public DeviceEventResubscriptionTests()
        {
            // 配置日志
            Log.Logger = new LoggerConfiguration()
                .WriteTo.Console()
                .CreateLogger();

            // 配置服务
            var services = new ServiceCollection();
            services.AddSingleton<ILogger>(Log.Logger);
            services.AddEasyCaching(options =>
            {
                options.UseInMemory("default");
            });
            services.AddSingleton<IMessageBusManager, MessageBusManager>();
            services.AddSingleton<IMessageBusFactory, MessageBusFactory>();
            services.AddSingleton<EquipmentCommunicationManager>();
            services.AddSingleton<EquipmentInitializer>();
            services.AddSingleton<IotEventIntegrationService>();

            _serviceProvider = services.BuildServiceProvider();
            _messageBusManager = _serviceProvider.GetRequiredService<IMessageBusManager>();
            _communicationManager = _serviceProvider.GetRequiredService<EquipmentCommunicationManager>();
            _iotEventIntegration = _serviceProvider.GetRequiredService<IotEventIntegrationService>();

            // 初始化测试数据
            InitializeTestData();
        }

        private void InitializeTestData()
        {
            using var db = new GcpDb();
            
            // 创建测试消息事件
            var messageEvent = new LcMessageEvent
            {
                Id = _testEventId,
                EventName = "测试设备变化事件",
                SourceType = 1, // 设备类型
                EventType = 2,  // 设备状态变化
                IsEnabled = 1,
                State = 1,
                SolutionId = "test-solution",
                ProjectId = "test-project",
                FunctionId = "test-function"
            };

            // 创建测试事件映射
            var eventMapping = new LcMessageEventMapping
            {
                Id = "test-mapping-001",
                EventId = _testEventId,
                SourceId = _testEquipmentId,
                SourceCode = "TestEquipment",
                State = 1,
                SolutionId = "test-solution",
                ProjectId = "test-project"
            };

            // 创建测试设备
            var equipment = new LcIotEquipment
            {
                Id = _testEquipmentId,
                EquipmentCode = "TestEquipment",
                EquipmentName = "测试设备",
                EquipmentType = "TestType",
                DriverCode = "TestDriver",
                Status = 1,
                State = 1,
                SolutionId = "test-solution",
                ProjectId = "test-project"
            };

            db.BeginTransaction();
            try
            {
                // 清理可能存在的测试数据
                db.LcMessageEvents.Where(e => e.Id == _testEventId).Delete();
                db.LcMessageEventMappings.Where(m => m.Id == "test-mapping-001").Delete();
                db.LcIotEquipment.Where(e => e.Id == _testEquipmentId).Delete();

                // 插入测试数据
                db.Insert(messageEvent);
                db.Insert(eventMapping);
                db.Insert(equipment);

                db.CommitTransaction();
            }
            catch
            {
                db.RollbackTransaction();
                throw;
            }
        }

        [Fact]
        public async Task DeviceOfflineOnline_ShouldResubscribeEvents()
        {
            // Arrange
            var eventSubscribed = false;
            var eventResubscribed = false;

            // 模拟设备初始上线和事件订阅
            await _iotEventIntegration.CreateDeviceEventConsumerAsync(
                await GetTestMessageEvent(),
                await GetTestEventMapping()
            );
            eventSubscribed = true;

            // Act - 模拟设备离线
            EquipmentCommunicationTask.OnDeviceOnlineStatusChanged?.Invoke(_testEquipmentId, false);
            await Task.Delay(100); // 等待异步处理完成

            // 模拟设备重新上线
            EquipmentCommunicationTask.OnDeviceOnlineStatusChanged?.Invoke(_testEquipmentId, true);
            await Task.Delay(100); // 等待异步处理完成
            eventResubscribed = true;

            // Assert
            Assert.True(eventSubscribed, "事件应该已经订阅");
            Assert.True(eventResubscribed, "设备重新上线后事件应该重新订阅");
        }

        [Fact]
        public async Task DeviceOffline_ShouldClearEventSubscriptions()
        {
            // Arrange
            await _iotEventIntegration.CreateDeviceEventConsumerAsync(
                await GetTestMessageEvent(),
                await GetTestEventMapping()
            );

            // Act - 模拟设备离线
            EquipmentCommunicationTask.OnDeviceOnlineStatusChanged?.Invoke(_testEquipmentId, false);
            await Task.Delay(100); // 等待异步处理完成

            // Assert
            // 验证事件订阅已被清理（这里需要通过反射或其他方式验证内部状态）
            // 由于 _subscribedHandlers 是私有字段，我们通过重新订阅来验证清理是否成功
            var canResubscribe = await _iotEventIntegration.CreateDeviceEventConsumerAsync(
                await GetTestMessageEvent(),
                await GetTestEventMapping()
            );

            Assert.True(canResubscribe, "设备离线后应该能够重新订阅事件");
        }

        private async Task<LcMessageEvent> GetTestMessageEvent()
        {
            using var db = new GcpDb();
            return await db.LcMessageEvents.FirstOrDefaultAsync(e => e.Id == _testEventId);
        }

        private async Task<LcMessageEventMapping> GetTestEventMapping()
        {
            using var db = new GcpDb();
            return await db.LcMessageEventMappings.FirstOrDefaultAsync(m => m.EventId == _testEventId);
        }

        public void Dispose()
        {
            // 清理测试数据
            using var db = new GcpDb();
            db.LcMessageEvents.Where(e => e.Id == _testEventId).Delete();
            db.LcMessageEventMappings.Where(m => m.Id == "test-mapping-001").Delete();
            db.LcIotEquipment.Where(e => e.Id == _testEquipmentId).Delete();

            _iotEventIntegration?.Dispose();
            _serviceProvider?.Dispose();
        }
    }
}
