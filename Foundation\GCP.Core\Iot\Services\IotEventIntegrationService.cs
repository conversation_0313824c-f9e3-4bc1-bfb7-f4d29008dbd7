using GCP.Common;
using GCP.DataAccess;
using GCP.Iot.Models;
using LinqToDB;
using Serilog;

namespace GCP.Iot.Services
{
    /// <summary>
    /// IoT事件集成服务，用于将设备事件集成到消息总线
    /// </summary>
    class IotEventIntegrationService : IDisposable
    {
        private readonly IMessageBusManager _messageBusManager;
        private readonly EquipmentCommunicationManager _communicationManager;
        private readonly EquipmentInitializer _equipmentInitializer;
        private readonly IMessageBus _iotBus;


        public IotEventIntegrationService(
            IMessageBusManager messageBusManager,
            EquipmentCommunicationManager communicationManager,
            EquipmentInitializer equipmentInitializer)
        {
            _messageBusManager = messageBusManager;
            _communicationManager = communicationManager;
            _equipmentInitializer = equipmentInitializer;

            // 获取Iot消息总线
            _iotBus = messageBusManager.MessageBuses[EventBusHelper.LocalIotEventBusName]
                ?? throw new InvalidOperationException("Iot消息总线未初始化");

            // 订阅设备在线状态变化事件
            EquipmentCommunicationTask.OnDeviceOnlineStatusChanged += HandleDeviceOnlineStatusChanged;
        }

        /// <summary>
        /// 为设备事件创建消息总线消费者
        /// </summary>
        public async Task<bool> CreateDeviceEventConsumerAsync(
            LcMessageEvent evt,
            LcMessageEventMapping mapping,
            CancellationToken cancellationToken = default)
        {
            var equipmentCode = "";
            try
            {
                // 确保是设备类型的事件
                if (evt.SourceType != 1) // 1 表示设备类型
                {
                    return false;
                }

                await using var db = new GcpDb();
                var equipmentInfo = evt.EventType == 3 ? await db.LcIotEquipment.FirstOrDefaultAsync(e => e.EquipmentType == mapping.SourceId, token: cancellationToken) : await db.LcIotEquipment
                    .FirstOrDefaultAsync(e => e.Id == mapping.SourceId, token: cancellationToken);
                equipmentCode = evt.EventType == 3 ? equipmentInfo.EquipmentType : equipmentInfo?.EquipmentCode;

                // 获取设备所属的驱动ID
                var driverCode = equipmentInfo?.DriverCode;
                if (string.IsNullOrEmpty(driverCode))
                {
                    Log.Warning("无法找到设备 {MappingSourceId} 对应的驱动", mapping.SourceId);
                    return false;
                }

                // 检查驱动是否由当前实例处理
                if (!await _equipmentInitializer.ShouldHandleDriverAsync(driverCode))
                {
                    Log.Information("设备 {MappingSourceId} 的驱动 {DriverCode} 由其他实例处理", mapping.SourceId, driverCode);
                    return false;
                }

                // 检查消费者是否存在且正在运行
                var needCreateConsumer = false;
                if (!_messageBusManager.Consumers.ContainsKey(evt.Id))
                {
                    needCreateConsumer = true;
                    Log.Debug("消费者不存在，需要创建: {EvtId}", evt.Id);
                }
                else
                {
                    var existingConsumer = _messageBusManager.Consumers[evt.Id];
                    if (!existingConsumer.IsRunning)
                    {
                        Log.Warning("消费者已停止，需要重新启动: {EvtId}", evt.Id);
                        try
                        {
                            // 尝试重新启动现有消费者
                            await existingConsumer.StartAsync(cancellationToken);
                            Log.Information("消费者已重新启动: {EvtId}", evt.Id);
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex, "重新启动消费者失败，将重新创建: {EvtId}", evt.Id);
                            // 移除失效的消费者
                            await _messageBusManager.RemoveConsumerAsync(evt.Id, cancellationToken);
                            needCreateConsumer = true;
                        }
                    }
                    else
                    {
                        Log.Debug("消费者已存在且正在运行: {EvtId}", evt.Id);
                    }
                }

                if (needCreateConsumer)
                {
                    var consumerOptions = new ConsumerOptions
                    {
                        Name = evt.Id,
                        Topic = evt.EventName,
                        ConsumerId = evt.FunctionId,
                        BusName = EventBusHelper.LocalIotEventBusName,
                        IsEnabled = evt.IsEnabled == 1,
                        IsFlow = true,
                        //ConcurrentCount = 1,
                        //RetryOptions = new RetryOptions
                        //{
                        //    MaxRetries = 3,
                        //    DelayMilliseconds = 1000,
                        //    ExponentialBackoff = true
                        //},
                        Settings = new Dictionary<string, string>
                        {
                            { "SolutionId", evt.SolutionId },
                            { "ProjectId", evt.ProjectId },
                            { "DriverCode", driverCode },
                        }
                    };

                    // 创建消费者
                    _ = await _messageBusManager.AddConsumerAsync(consumerOptions, cancellationToken);
                    Log.Information("已创建新的消费者: {EvtId}", evt.Id);
                }

                // 订阅设备事件
                await SubscribeDeviceEventAsync(evt, mapping);

                Log.Information("已为设备 {EquipmentCode} (驱动 {DriverCode}) 创建事件消费者: {EvtEventName}", equipmentCode, driverCode, evt.EventName);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "为设备 {EquipmentCode} 创建事件消费者失败: {EvtEventName}", equipmentCode, evt.EventName);
                return false;
            }
        }

        private readonly Dictionary<string, Delegate> _eventHandlers = new();
        private readonly HashSet<string> _subscribedHandlers = new();

        /// <summary>
        /// 订阅设备事件
        /// </summary>
        private Task SubscribeDeviceEventAsync(LcMessageEvent evt, LcMessageEventMapping mapping)
        {
            var equipmentId = mapping.SourceId;
            var handlerKey = mapping.Id;

            // 检查是否已经订阅过此事件处理器
            if (_subscribedHandlers.Contains(handlerKey))
            {
                Log.Debug("事件处理器已存在，跳过重复订阅: {HandlerKey}", handlerKey);
                return Task.CompletedTask;
            }

            // 根据事件类型订阅不同的设备事件
            switch (evt.EventType)
            {
                case 1: // 设备变量变化
                    var variableCode = mapping.SourceCode;
                    if (!_eventHandlers.TryGetValue(handlerKey, out var variableHandler))
                    {
                        EventHandler<VariableValueChangedEventArgs> eventHandler =
                            (sender, e) =>
                            {
                                _ = PublishEventMessageAsync(evt, mapping, e);
                            };

                        variableHandler = eventHandler;
                        _eventHandlers[handlerKey] = eventHandler;
                    }

                    // 先移除可能存在的旧处理器，再添加新的
                    _communicationManager.EventManager.RemoveVariableHandler(equipmentId, variableCode, (EventHandler<VariableValueChangedEventArgs>)variableHandler);
                    _communicationManager.EventManager.AddVariableHandler(equipmentId, variableCode, (EventHandler<VariableValueChangedEventArgs>)variableHandler);
                    break;

                case 2: // 设备状态变化
                    if (!_eventHandlers.TryGetValue(handlerKey, out var equipmentHandler))
                    {
                        EventHandler<EquipmentDataChangedEventArgs> eventHandler =
                            (sender, e) =>
                            {
                                _ = PublishEventMessageAsync(evt, mapping, e);
                            };

                        equipmentHandler = eventHandler;
                        _eventHandlers[handlerKey] = eventHandler;
                    }

                    // 先移除可能存在的旧处理器，再添加新的
                    _communicationManager.EventManager.RemoveEquipmentHandler(equipmentId, (EventHandler<EquipmentDataChangedEventArgs>)equipmentHandler);
                    _communicationManager.EventManager.AddEquipmentHandler(equipmentId, (EventHandler<EquipmentDataChangedEventArgs>)equipmentHandler);
                    break;

                case 3: // 设备类型事件
                    var equipmentType = mapping.SourceId;
                    if (!_eventHandlers.TryGetValue(handlerKey, out var typeHandler))
                    {
                        EventHandler<EquipmentDataChangedEventArgs> eventHandler =
                            (sender, e) =>
                            {
                                _ = PublishEventMessageAsync(evt, mapping, e);
                            };

                        typeHandler = eventHandler;
                        _eventHandlers[handlerKey] = eventHandler;
                    }

                    // 先移除可能存在的旧处理器，再添加新的
                    _communicationManager.EventManager.RemoveEquipmentTypeHandler(equipmentType, (EventHandler<EquipmentDataChangedEventArgs>)typeHandler);
                    _communicationManager.EventManager.AddEquipmentTypeHandler(equipmentType, (EventHandler<EquipmentDataChangedEventArgs>)typeHandler);
                    break;
            }

            // 标记此处理器已订阅
            _subscribedHandlers.Add(handlerKey);
            return Task.CompletedTask;
        }

        public Task UnsubscribeDeviceEventAsync(LcMessageEvent evt, LcMessageEventMapping mapping)
        {
            var equipmentId = mapping.SourceId;
            var handlerKey = mapping.Id;

            if (!_eventHandlers.TryGetValue(handlerKey, out var handler))
                return Task.CompletedTask;

            switch (evt.EventType)
            {
                case 1: // 设备变量变化
                    var variableCode = mapping.SourceCode;
                    _communicationManager.EventManager.RemoveVariableHandler(equipmentId, variableCode, (EventHandler<VariableValueChangedEventArgs>)handler);
                    break;
                case 2: // 设备状态变化
                    _communicationManager.EventManager.RemoveEquipmentHandler(equipmentId, (EventHandler<EquipmentDataChangedEventArgs>)handler);
                    break;
                case 3: // 设备类型事件
                    var equipmentType = mapping.SourceId;
                    _communicationManager.EventManager.RemoveEquipmentTypeHandler(equipmentType, (EventHandler<EquipmentDataChangedEventArgs>)handler);
                    break;
            }

            // 清理订阅状态
            _eventHandlers.Remove(handlerKey);
            _subscribedHandlers.Remove(handlerKey);

            Log.Debug("已取消订阅设备事件处理器: {HandlerKey}", handlerKey);
            return Task.CompletedTask;
        }

        private async Task PublishEventMessageAsync(LcMessageEvent evt, LcMessageEventMapping mapping, object message)
        {
            try
            {
                await _iotBus.PublishAsync(evt.EventName, message);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "发布设备事件消息失败: {EvtEventName}, 设备(类型): {MappingSourceId}", evt.EventName, mapping.SourceId);
            }
        }

        /// <summary>
        /// 清理指定设备的所有事件订阅
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        public void ClearDeviceEventSubscriptions(string equipmentId)
        {
            var handlersToRemove = _eventHandlers
                .Where(kvp => kvp.Key.Contains(equipmentId))
                .ToList();

            foreach (var handler in handlersToRemove)
            {
                _eventHandlers.Remove(handler.Key);
                _subscribedHandlers.Remove(handler.Key);
            }

            // 清理通信管理器中的事件处理器
            _communicationManager.EventManager.ClearEquipmentHandlers(equipmentId);

            Log.Debug("已清理设备的所有事件订阅: {EquipmentId}", equipmentId);
        }

        /// <summary>
        /// 处理设备在线状态变化
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="isOnline">是否在线</param>
        private async void HandleDeviceOnlineStatusChanged(string equipmentId, bool isOnline)
        {
            try
            {
                if (isOnline)
                {
                    // 设备重新上线，重新订阅相关的消息事件
                    await ResubscribeDeviceEventsAsync(equipmentId);
                }
                else
                {
                    // 设备离线，清理事件订阅状态
                    ClearDeviceEventSubscriptions(equipmentId);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "处理设备状态变化失败: {EquipmentId}, IsOnline: {IsOnline}", equipmentId, isOnline);
            }
        }

        /// <summary>
        /// 重新订阅设备相关的消息事件
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        private async Task ResubscribeDeviceEventsAsync(string equipmentId)
        {
            try
            {
                await using var db = new GcpDb();

                // 查找与该设备相关的所有启用的消息事件
                var deviceEvents = await db.LcMessageEvents
                    .Where(e => e.SourceType == 1 && e.IsEnabled == 1 && e.State == 1)
                    .ToListAsync();

                foreach (var evt in deviceEvents)
                {
                    var mappings = await db.LcMessageEventMappings
                        .Where(m => m.EventId == evt.Id && m.SourceId == equipmentId && m.State == 1)
                        .ToListAsync();

                    foreach (var mapping in mappings)
                    {
                        // 清理旧的订阅状态
                        var handlerKey = mapping.Id;
                        _subscribedHandlers.Remove(handlerKey);

                        // 重新订阅事件
                        await SubscribeDeviceEventAsync(evt, mapping);
                        Log.Information("已重新订阅设备事件: 设备ID={EquipmentId}, 事件={EventName}", equipmentId, evt.EventName);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "重新订阅设备事件失败: {EquipmentId}", equipmentId);
            }
        }

        public void Dispose()
        {
            // 取消订阅设备状态变化事件
            EquipmentCommunicationTask.OnDeviceOnlineStatusChanged -= HandleDeviceOnlineStatusChanged;
        }
    }
}